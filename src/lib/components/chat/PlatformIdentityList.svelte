<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';
	import InfiniteScroll from '../common/InfiniteScroll.svelte';
	import FilterPanel from './FilterPanel.svelte';
	import type { CustomerPlatformIdentity, Message } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import { conversationStore } from '$lib/stores/conversationStore';
	import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';
	import {
		AdjustmentsHorizontalSolid,
		ArrowUpDownOutline,
		UserHeadsetOutline,
		BullhornOutline,
		TicketSolid,
		RefreshOutline,
		SearchOutline,
		ExclamationCircleOutline
	} from 'flowbite-svelte-icons';
	import { Badge, Tooltip } from 'flowbite-svelte';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import { invalidate, invalidateAll } from '$app/navigation';
	import { PollingService } from '$lib/services/pollingService';

	export let platformIdentities: CustomerPlatformIdentity[] = [];
	export let selectedPlatformId: number | null = null;
	export let hasMore: boolean = false;
	export let currentUserFullName: string = '';

	const dispatch = createEventDispatcher();

	let searchTerm = '';
	let latestMessages: Map<number, Message> = new Map();
	let unreadCounts: Map<number, number> = new Map();
	let loadingMore = false;

	// Polling service instance and state
	const pollingService = PollingService.getInstance();
	let isPollingEnabled = false;

	// Avatar image error tracking
	let imageErrors = new Set<number>();

	// Tab state
	let activeTab = 'my-assigned';

	// Tab definitions
	const tabs = [
		{ id: 'my-assigned', label: 'My Assigned', key: 'chat_center_filter_tab_my_assigned' },
		{ id: 'my-closed', label: 'My Closed', key: 'chat_center_filter_tab_my_closed' },
		{ id: 'open', label: 'Open', key: 'chat_center_filter_tab_open' },
		{
			id: 'others-assigned',
			label: 'Others Assigned',
			key: 'chat_center_filter_tab_all_assigned'
		}
	];

	// Sort and filter state
	let sortBy = 'recent'; // 'recent', 'name', 'platform'
	let sortDirection = 'desc'; // 'asc', 'desc'
	let showFilterDropdown = false;
	let showFilterPanel = false;
	let activeFiltersCount = 0;
	let filterData = {
		dateRange: null,
		customStartDate: null,
		customEndDate: null,
		platforms: new Set(['All']),
		channels: new Set(['All']),
		unreadFilter: new Set(['All']),
		statuses: new Set(['All']),
		tags: new Set(['All']),
		owners: new Set(['All']),
		priorities: new Set(['All']),
		searchText: ''
	};

	$: filteredIdentities = filterIdentities(platformIdentities, searchTerm, filterData);
	$: tabFilteredIdentities = filterByTab(filteredIdentities, activeTab, currentUserFullName);
	$: sortedIdentities = sortIdentities(tabFilteredIdentities, latestMessages);

	// Reactive statement to refresh additional data when platformIdentities change
	// $: if (platformIdentities.length > 0) {
		// Only refresh if we're not already in the middle of a refresh
		// This prevents infinite loops when invalidateAll() updates platformIdentities
		// if (!refreshing) {
			//loadAdditionalData();
		// }
	// }

	onMount(() => {
		// Initial data load
		loadAdditionalData();

		// Start polling service for periodic updates
		startPlatformPolling();

		// Only access window in browser environment
		if (typeof window !== 'undefined') {
			// Listen for WebSocket events for real-time updates
			window.addEventListener('platform-new-message', handleNewMessage);
			window.addEventListener('platform-status-update', handleStatusUpdate);
			window.addEventListener('platform-typing', handleTypingIndicator);
			window.addEventListener('platform-bulk-update', handleBulkUpdate);

			// Subscribe to all visible platforms for real-time updates
			const visiblePlatformIds = platformIdentities.map((p) => p.id);
			if (visiblePlatformIds.length > 0) {
				platformWebSocket.subscribeToMultiplePlatforms(visiblePlatformIds);
			}
		}
	});

	onDestroy(() => {
		// Stop polling service
		stopPlatformPolling();

		// Only access window in browser environment
		if (typeof window !== 'undefined') {
			// Clean up event listeners
			window.removeEventListener('platform-new-message', handleNewMessage);
			window.removeEventListener('platform-status-update', handleStatusUpdate);
			window.removeEventListener('platform-typing', handleTypingIndicator);
			window.removeEventListener('platform-bulk-update', handleBulkUpdate);
		}
	});

	async function loadAdditionalData() {
		await Promise.all([loadLatestIdentities()]);

		// Load latest messages and unread counts for all platform identities
		const platformIds = platformIdentities.map((p) => p.id);
		if (platformIds.length > 0) {
			await Promise.all([loadLatestMessages(platformIds), loadUnreadCounts(platformIds)]);
		}
	}

	/**
	 * Custom fetcher function for polling service
	 * Combines the current data loading logic for periodic updates
	 */
	async function fetchPlatformDataForPolling(): Promise<any> {
		try {
			// console.log('PlatformIdentityList.svelte: fetchPlatformDataForPolling(): Starting data refresh...');

			// First, refresh server-side data (platformIdentities)
			await invalidateAll();

			// Wait a brief moment for the server data to be updated
			await new Promise((resolve) => setTimeout(resolve, 100));

			// Then refresh local data (latest messages and unread counts)
			await loadAdditionalData();

			// console.log('PlatformIdentityList.svelte: fetchPlatformDataForPolling(): Data refresh completed successfully');

			// Return a simple success indicator
			return { success: true, timestamp: new Date() };
		} catch (error) {
			console.error('PlatformIdentityList.svelte: fetchPlatformDataForPolling(): Error during data refresh:', error);
			throw error;
		}
	}

	/**
	 * Handle polling data updates
	 */
	function handlePollingDataUpdate(data: any): void {
		// console.log('PlatformIdentityList.svelte: handlePollingDataUpdate(): Received polling update', data);
		// The actual data update happens in fetchPlatformDataForPolling
		// This callback is just for logging/notification purposes
	}

	/**
	 * Handle polling errors
	 */
	function handlePollingError(error: Error): void {
		console.error('PlatformIdentityList.svelte: Polling error:', error);
		// Don't show toast for polling errors to avoid spam
	}

	/**
	 * Start platform data polling using shared service
	 */
	function startPlatformPolling(): void {
		try {
			// Register platform data polling endpoint with the shared service using custom fetcher
			const success = pollingService.registerEndpoint('platform-identity-list', {
				interval: 3000, // 3 seconds as requested
				onDataChange: handlePollingDataUpdate,
				onError: handlePollingError,
				debugMode: true, // Set to true for debugging
				customFetcher: fetchPlatformDataForPolling
			});

			if (success) {
				isPollingEnabled = true;
				console.log('PlatformIdentityList.svelte: Platform polling started successfully with custom fetcher');
			} else {
				console.error('PlatformIdentityList.svelte: Failed to start platform polling');
			}
		} catch (error) {
			console.error('PlatformIdentityList.svelte: Error starting platform polling:', error);
		}
	}

	/**
	 * Stop platform data polling
	 */
	function stopPlatformPolling(): void {
		try {
			if (isPollingEnabled) {
				pollingService.unregisterEndpoint('platform-identity-list');
				isPollingEnabled = false;
				console.log('PlatformIdentityList.svelte: Platform polling stopped');
			}
		} catch (error) {
			console.error('PlatformIdentityList.svelte: Error stopping platform polling:', error);
		}
	}

	async function loadLatestIdentities() {
		try {
			const response = await fetch(`${getBackendUrl()}/customer/api/platform-identities/`, {
				credentials: 'include'
			});

			if (response.ok) {
				const data = await response.json();
				platformIdentities = data.results;
				hasMore = !!data.next;
			}
		} catch (error) {
			console.error('Error loading latest identities:', error);
		}
	}

	async function loadLatestMessages(platformIds: number[]) {
		try {
			// Batch load latest messages
			const response = await fetch(
				`${getBackendUrl()}/customer/api/platform-messages/?platform_ids=${platformIds.join(',')}`,
				{ credentials: 'include' }
			);

			if (response.ok) {
				const data = await response.json();
				latestMessages = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as Message]));
			}
		} catch (error) {
			console.error('Error loading latest messages:', error);
		}
	}

	async function loadUnreadCounts(platformIds: number[]) {
		try {
			// Batch load unread counts
			const response = await fetch(
				`${getBackendUrl()}/customer/api/platform-unread-counts/?platform_ids=${platformIds.join(',')}`,
				{ credentials: 'include' }
			);

			if (response.ok) {
				const data = await response.json();
				unreadCounts = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as number]));
			}
		} catch (error) {
			console.error('Error loading unread counts:', error);
		}
	}



	function filterIdentities(identities: CustomerPlatformIdentity[], search: string, filters: any) {
		let filtered = identities;

		// Apply search filter from main search input
		if (search) {
			const searchLower = search.toLowerCase();
			filtered = filtered.filter(
				(p) =>
					// Search in platform username
					p.platform_username?.toLowerCase().includes(searchLower) ||
					// Search in channel name
					p.channel_name?.toLowerCase().includes(searchLower) ||
					// Search in ticket ID
					p.latest_ticket_id?.toString().includes(searchLower) ||
					// Search in ticket owner
					p.latest_ticket_owner?.toLowerCase().includes(searchLower) ||
					// Search in customer's fullname
					p.customer_fullname?.toLowerCase().includes(searchLower) ||
					// Search in customer's citizen ID
					p.customer_national_id?.includes(searchLower)
			);
		}

		// Apply platform filter
		if (filters.platforms && !filters.platforms.has('All')) {
			filtered = filtered.filter((p) => filters.platforms.has(p.platform));
		}

		// Apply channel filter
		if (filters.channels && !filters.channels.has('All')) {
			filtered = filtered.filter((p) => {
				const channelName = p.channel_name || 'No Channel';
				return filters.channels.has(channelName);
			});
		}

		// Apply unread messages filter
		if (filters.unreadFilter && !filters.unreadFilter.has('All')) {
			filtered = filtered.filter((p) => {
				const unreadCount = unreadCounts.get(p.id) || p.unread_count || 0;

				if (filters.unreadFilter.has('unread')) {
					return unreadCount > 0;
				}
				return true;
			});
		}

		// Apply date range filter
		if (filters.dateRange) {
			const now = new Date();
			let startDate: Date;

			switch (filters.dateRange) {
				case 'today':
					startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
					break;
				case 'yesterday':
					startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
					break;
				case 'week':
					startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
					break;
				case 'month':
					startDate = new Date(now.getFullYear(), now.getMonth(), 1);
					break;
				case 'last-3-months':
					startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1);
					break;
				case 'last-6-months':
					startDate = new Date(now.getFullYear(), now.getMonth() - 6, 1);
					break;
				case 'custom':
					if (filters.customStartDate) {
						startDate = new Date(filters.customStartDate);
					}
					break;
			}

			if (startDate) {
				filtered = filtered.filter((p) => {
					const messageTime =
						latestMessages.get(p.id)?.created_on || p.last_interaction || p.created_on;
					return messageTime && new Date(messageTime) >= startDate;
				});
			}

			// [Unused] Apply custom end date if specified
			// if (filters.dateRange === 'custom' && filters.customEndDate) {
			// 	const endDate = new Date(filters.customEndDate);
			// 	endDate.setHours(23, 59, 59, 999); // End of day
			// 	filtered = filtered.filter((p) => {
			// 		const messageTime =
			// 			latestMessages.get(p.id)?.created_on || p.last_interaction || p.created_on;
			// 		return messageTime && new Date(messageTime) <= endDate;
			// 	});
			// }
		}

		// Apply status filter
		if (filters.statuses && !filters.statuses.has('All')) {
			filtered = filtered.filter((p) => {
				const status = (p as any)['latest_ticket_status'];
				return status && filters.statuses.has(status);
			});
		}

		// Apply owner filter
		if (filters.owners && !filters.owners.has('All')) {
			filtered = filtered.filter((p) => {
				const fullOwner = (p as any)['latest_ticket_owner'];
				if (!fullOwner) return false; // Exclude identities without owners

				// Extract first word/name only to match the filter options
				const ownerFirstName = fullOwner.split(/\s+/)[0];
				return filters.owners.has(ownerFirstName);
			});
		}

		// Apply priority filter
		if (filters.priorities && !filters.priorities.has('All')) {
			filtered = filtered.filter((p) => {
				const priority = (p as any)['latest_ticket_priority'];
				if (!priority) return false; // Exclude identities without priority

				// Check if the priority matches one of the selected priorities
				return filters.priorities.has(priority);
			});
		}

		return filtered;
	}

	function filterByTab(identities: CustomerPlatformIdentity[], tab: string, userFullName: string) {
		if (!userFullName) return identities;

		return identities.filter((identity) => {
			// Use bracket notation to access properties that exist in runtime but not in types
			const status = (identity as any)['latest_ticket_status'];
			const owner = (identity as any)['latest_ticket_owner'];

			switch (tab) {
				case 'my-assigned':
					return (status === 'assigned' || status === 'pending_to_close') && owner === userFullName;
				case 'my-closed':
					return status === 'closed' && owner === userFullName;
				case 'open':
					return status === 'open';
				case 'others-assigned':
					return status !== 'open' && owner !== userFullName && owner != null;
				default:
					return true;
			}
		});
	}

	function sortIdentities(identities: CustomerPlatformIdentity[], messages: Map<number, Message>) {
		return [...identities].sort((a, b) => {
			const aMsg = messages.get(a.id);
			const bMsg = messages.get(b.id);

			// If both have messages, sort by message time
			if (aMsg && bMsg) {
				return new Date(bMsg.created_on).getTime() - new Date(aMsg.created_on).getTime();
			}

			// If only one has a message, it goes first
			if (aMsg && !bMsg) return -1;
			if (!aMsg && bMsg) return 1;

			// If neither has messages, sort by last interaction
			const aTime = a.last_interaction || a.created_on;
			const bTime = b.last_interaction || b.created_on;
			return new Date(bTime).getTime() - new Date(aTime).getTime();
		});
	}

	function handleIdentityClick(identity: CustomerPlatformIdentity) {
		// Note: Data refresh is now handled automatically by the polling service
		// No need for manual refresh on click as polling provides up-to-date data

		// Check if customer is just an ID or a full object
		let customerId: number;

		if (typeof identity.customer === 'number') {
			// If customer is just an ID, use it directly
			customerId = identity.customer;
		} else if (
			identity.customer &&
			typeof identity.customer === 'object' &&
			identity.customer.customer_id
		) {
			// If customer is an object with customer_id
			customerId = identity.customer.customer_id;
		} else {
			console.error('Platform identity missing valid customer data:', identity);
			return;
		}

		// Pass the entire identity object along with the extracted customerId
		dispatch('select', {
			platformId: identity.id,
			customerId: customerId,
			platformIdentity: identity
		});
	}

	function handleLoadMore() {
		if (!loadingMore && hasMore) {
			loadingMore = true;
			dispatch('loadMore');
			loadingMore = false;
		}
	}

	function handleNewMessage(event: CustomEvent) {
		const { platformId, message, unreadCount } = event.detail;
		// TODO - Delete this console log in production
		console.log(
			'PlatformIdentityList.svelte: handleNewMessage(): New message event:',
			event.detail
		);

		// Update latest message
		latestMessages.set(platformId, message);
		latestMessages = latestMessages; // Trigger reactivity

		// Update unread count
		unreadCounts.set(platformId, unreadCount);
		unreadCounts = unreadCounts; // Trigger reactivity

		// IMPORTANT: Add the message to conversation store
		// This ensures the message appears in the conversation view
		conversationStore.addMessage(platformId, message);

		// Re-sort the list to move updated platform to top
		sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
	}

	function handleStatusUpdate(event: CustomEvent) {
		const { platformId, status } = event.detail;

		// Update platform status if needed
		const platform = platformIdentities.find((p) => p.id === platformId);
		if (platform) {
			// You can add status to your platform type if needed
			// platform.status = status;
			platformIdentities = platformIdentities; // Trigger reactivity
		}
	}

	function handleTypingIndicator(event: CustomEvent) {
		const { platformId, isTyping, userName } = event.detail;
		// You can implement typing indicator UI here if needed
	}

	function handleBulkUpdate(event: CustomEvent) {
		const updates = event.detail;

		// Update multiple platforms at once
		Object.entries(updates).forEach(([platformId, data]: [string, any]) => {
			const id = parseInt(platformId);

			if (data.latest_message) {
				latestMessages.set(id, data.latest_message);
			}

			if (data.unread_count !== undefined) {
				unreadCounts.set(id, data.unread_count);
			}
		});

		// Trigger reactivity
		latestMessages = latestMessages;
		unreadCounts = unreadCounts;
		sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
	}

	function formatTime(dateString: string): string {
		const lang = get(language); // 'en' or 'th'
		const date = new Date(dateString);
		const now = new Date();
		const diff = now.getTime() - date.getTime();

		// Define localized labels
		const labels = {
			en: {
				justNow: 'Just now',
				minutesAgo: (m: number) => `${m}m ago`,
				hoursAgo: (h: number) => `${h}h ago`,
				daysAgo: (d: number) => `${d}d ago`,
				yesterday: 'Yesterday'
			},
			th: {
				justNow: 'เมื่อสักครู่',
				minutesAgo: (m: number) => `${m} นาทีที่แล้ว`,
				hoursAgo: (h: number) => `${h} ชั่วโมงที่แล้ว`,
				daysAgo: (d: number) => `${d} วันที่แล้ว`,
				yesterday: 'เมื่อวานนี้'
			}
		};

		// Fallback to 'en' if lang is not supported
		const currentLang = ['en', 'th'].includes(lang) ? lang : 'en';
		const l = labels[currentLang as 'en' | 'th'];

		if (diff < 60000) return l.justNow;
		if (diff < 3600000) return l.minutesAgo(Math.floor(diff / 60000));
		if (diff > 86400000 && diff < 172800000) return l.yesterday;
		if (diff < 86400000) return l.hoursAgo(Math.floor(diff / 3600000));
		if (diff < 604800000) return l.daysAgo(Math.floor(diff / 86400000));

		// If more than a week, format as date in local language
		return date.toLocaleDateString(lang === 'th' ? 'th-TH' : 'en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	function getPlatformIcon(platform: string): string {
		const icons = {
			LINE: '/images/platform-line.png',
			WHATSAPP: '/images/platform-whatsapp.png',
			FACEBOOK: '/images/platform-facebook.png',
			INSTAGRAM: '/images/platform-instagram.png'
		};
		return icons[platform] || '/images/platform-line.png';
	}

	function getCustomerName(identity: CustomerPlatformIdentity): string {
		return identity.platform_username || 'Unknown Customer';
	}

	function handleImageError(identityId: number) {
		imageErrors.add(identityId);
		imageErrors = imageErrors; // Trigger reactivity
	}

	function isValidImageUrl(url: string | undefined): boolean {
		if (!url) return false;
		// Basic URL validation
		try {
			new URL(url);
			return true;
		} catch {
			return false;
		}
	}

	// Sort and filter handlers
	function handleSort() {
		// Toggle sort direction if already sorting by recent, otherwise set to recent
		if (sortBy === 'recent') {
			sortDirection = sortDirection === 'desc' ? 'asc' : 'desc';
		} else {
			sortBy = 'recent';
			sortDirection = 'desc';
		}
		// Re-sort the identities
		sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
	}

	function handleFilter(event: MouseEvent) {
		event.stopPropagation();
		showFilterPanel = !showFilterPanel;
	}

	function handleFilterApply(event: CustomEvent) {
		filterData = event.detail;
		// The reactive statement will automatically update filteredIdentities
	}

	function handleFilterClose() {
		showFilterPanel = false;
	}

	// For Debugging
	// $: {
	// 	console.log('PlatformIdentityList.svelte: Variables: platformIdentities:', platformIdentities);
	// 	console.log('PlatformIdentityList.svelte: Variables: filteredIdentities:', filteredIdentities);
	// 	console.log('PlatformIdentityList.svelte: Variables: sortedIdentities:', sortedIdentities);
	// }
</script>

<div
	id="platform-list-platform-identity-list"
	class="flex h-full flex-col"
	data-testid="platform-identity-list"
>
	<!-- Header with Search -->
	<div
		id="platform-list-chat-center-header"
		class="min-h-[125px] border-b border-gray-200 p-4"
		data-testid="chat-center-header"
	>
		<div
			id="platform-list-chat-center-title-container"
			class="mb-3 flex items-center justify-between"
			data-testid="chat-center-title-container"
		>
			<h2
				id="platform-list-chat-center-title"
				class="text-lg font-semibold"
				data-testid="chat-center-title"
			>
				{t('chat_center')}
			</h2>
			<!-- For Debugging -->
			<!-- {#if refreshing}
				<div class="flex items-center text-sm text-blue-600">
					<svg class="mr-2 h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
						></circle>
						<path
							class="opacity-75"
							fill="currentColor"
							d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
						></path>
					</svg>
					Refreshing...
				</div>
			{/if} -->
		</div>
		<div
			id="platform-list-chat-center-search-container"
			class="flex items-center gap-1"
			data-testid="chat-center-search-container"
		>
			<div
				id="platform-list-search-input-container"
				class="relative flex-grow"
				data-testid="search-input-container"
			>
				<input
					id="platform-list-chat-center-search-input"
					type="text"
					bind:value={searchTerm}
					placeholder={t('chat_center_search_placeholder')}
					class="w-full rounded-lg border border-gray-300 px-3 py-2 pl-9 text-sm text-gray-600
                           focus:outline-none focus:ring-2 focus:ring-blue-500"
					aria-label="Search conversations"
					aria-describedby="platform-list-search-tooltip"
					role="searchbox"
					autocomplete="off"
					data-testid="chat-center-search-input"
				/>
				<SearchOutline
					id="platform-list-search-icon"
					class="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
					data-testid="search-icon"
				/>
				<Tooltip
					triggeredBy="#platform-list-chat-center-search-input"
					trigger="focus"
					id="platform-list-search-tooltip"
					placement="bottom"
					class="bg-gray-600 text-sm"
					data-testid="search-tooltip"
				>
					{t('chat_center_search_tooltip')}
				</Tooltip>
			</div>
			<!-- Refresh is now handled automatically by polling service -->
			<div
				id="platform-list-filter-container"
				class="relative flex-shrink-0"
				data-testid="filter-container"
			>
				<button
					id="platform-list-filter-button"
					type="button"
					on:click={handleFilter}
					class="flex cursor-pointer items-center justify-center gap-1 rounded-lg border border-gray-300 px-2 py-2 text-sm
                           transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500
                           {showFilterPanel ? 'border-blue-300 bg-blue-50' : activeFiltersCount > 0 ? 'bg-gray-700 text-white border-gray-700 hover:bg-gray-600' : ''}"
					aria-label="Filter conversations"
					aria-expanded={showFilterPanel}
					data-testid="filter-button"
				>
					<AdjustmentsHorizontalSolid
						id="platform-list-filter-icon"
						class="h-5 w-5 {activeFiltersCount > 0 && !showFilterPanel ? 'text-white' : 'text-gray-600'}"
						data-testid="filter-icon"
					/>
				</button>

				<FilterPanel
					bind:isOpen={showFilterPanel}
					bind:filterData
					bind:activeFiltersCount
					{sortedIdentities}
					{unreadCounts}
					on:apply={handleFilterApply}
					on:close={handleFilterClose}
				/>
			</div>
		</div>
	</div>

	<!-- Tab Navigation -->
	<div
		id="platform-list-chat-tabs-container"
		class="flex-shrink-0 border-b border-gray-200 bg-white"
		data-testid="chat-tabs-container"
	>
		<nav id="platform-list-chat-tabs" class="flex w-full" data-testid="chat-tabs">
			{#each tabs as tab}
				<button
					id="platform-list-chat-tab-{tab.id}"
					on:click={() => (activeTab = tab.id)}
					class="flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-center text-sm font-medium transition-colors
						{activeTab === tab.id
						? 'border-black bg-white text-black'
						: 'border-transparent text-gray-500 hover:text-gray-700'}"
					data-testid="chat-tab-{tab.id}"
					aria-selected={activeTab === tab.id}
					role="tab"
				>
					{t(tab.key)}
				</button>
			{/each}
		</nav>
	</div>

	<!-- Tab Content -->
	<div
		id="platform-list-chat-content-container"
		class="w-full flex-1 overflow-y-auto bg-gray-50"
		data-testid="chat-content-container"
	>
		<div
			id="platform-list-chat-content-wrapper"
			class="h-full w-full"
			data-testid="chat-content-wrapper"
		>
			<div
				id="platform-list-chat-content-scrollable"
				class="custom-scrollbar flex-1 overflow-y-auto"
				data-testid="platform-identity-list-content"
			>
				{#if sortedIdentities.length === 0}
					<div
						id="platform-list-empty-state"
						class="p-8 text-center text-gray-500"
						data-testid="empty-state"
					>
						{t('chat_center_filter_empty')}
					</div>
				{:else}
					<div
						id="platform-list-chat-items-list"
						class="divide-y divide-gray-100"
						data-testid="chat-items-list"
					>
						{#each sortedIdentities as identity (identity.id)}
							<button
								id="platform-list-chat-item-{identity.id}"
								class="chat-item relative w-full p-4 text-left transition-colors
									   {selectedPlatformId === identity.id
									? 'bg-blue-100 pl-6 hover:bg-blue-100'
									: 'hover:bg-gray-100'}"
								on:click={() => handleIdentityClick(identity)}
								data-testid="chat-item{selectedPlatformId === identity.id ? '-selected' : ''}"
								data-identity-id={identity.id}
								data-ticket-id={identity['latest_ticket_id']}
								data-customer-name={identity.display_name || identity.platform_username}
							>
								{#if selectedPlatformId === identity.id}
									<div
										id="platform-list-chat-item-selected-indicator-{identity.id}"
										class="absolute left-0 top-0 flex h-full w-1 items-center justify-center bg-blue-500"
										data-testid="chat-item-selected-indicator"
									></div>
								{/if}
								<div
									id="platform-list-chat-item-content-{identity.id}"
									class="flex items-start justify-between"
									data-testid="chat-item-content"
								>
									<!-- Avatar with Platform Icon Overlay -->
									<div
										id="platform-list-chat-item-avatar-container-{identity.id}"
										class="mr-3 flex-shrink-0"
										data-testid="chat-item-avatar-container"
									>
										<div
											id="platform-list-chat-item-avatar-wrapper-{identity.id}"
											class="relative"
											data-testid="chat-item-avatar-wrapper"
										>
											<!-- Main Avatar -->
											<div
												id="platform-list-chat-item-avatar-{identity.id}"
												class="h-12 w-12 overflow-hidden rounded-full bg-gray-100"
												data-testid="chat-item-avatar"
											>
												{#if isValidImageUrl(identity.platform_avatar_url) && !imageErrors.has(identity.platform_username)}
													<img
														id="platform-list-chat-item-avatar-image-{identity.id}"
														src={identity.platform_avatar_url}
														alt="{getCustomerName(identity)} avatar"
														class="h-full w-full rounded-full object-cover"
														on:error={() => handleImageError(identity.id)}
														data-testid="chat-item-avatar-image"
													/>
												{:else}
													<!-- Fallback initials when no picture_url or image failed to load -->
													<div
														id="platform-list-chat-item-avatar-initials-{identity.id}"
														class="flex h-full w-full items-center justify-center rounded-full bg-gray-100 font-medium text-gray-600"
														data-testid="chat-item-avatar-initials"
													>
														{getInitials(identity.platform_username)}
													</div>
												{/if}
											</div>
											<!-- Platform Icon Overlay -->
											<div
												id="platform-list-chat-item-platform-icon-container-{identity.id}"
												class="absolute -bottom-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full border border-white bg-white shadow-sm"
												data-testid="chat-item-platform-icon-container"
											>
												<img
													id="platform-list-chat-item-platform-icon-{identity.id}"
													src={getPlatformIcon(identity.platform)}
													alt="{identity.platform} icon"
													class="h-4 w-4 rounded-full object-cover"
													data-testid="chat-item-platform-icon"
												/>
											</div>
										</div>
									</div>
									<div
										id="platform-list-chat-item-info-{identity.id}"
										class="min-w-0 flex-1 pr-2"
										data-testid="chat-item-info"
									>
										<!-- Customer and Platform Info -->
										<div
											id="platform-list-chat-item-customer-info-{identity.id}"
											class="mb-1 flex items-center gap-2"
											data-testid="chat-item-customer-info"
										>
											<span
												id="platform-list-chat-item-customer-name-{identity.id}"
												class="truncate font-medium {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-900'}"
												data-testid="chat-item-customer-name"
											>
												{identity.display_name ||
													identity.platform_username ||
													identity.platform_user_id}
											</span>
										</div>

										<!-- Latest Message Preview -->
										{#if latestMessages.has(identity.id)}
											{@const message = latestMessages.get(identity.id)}
											<div
												id="platform-list-chat-item-latest-message-{identity.id}"
												class="mt-1 truncate text-sm {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-600'}"
												data-testid="chat-item-latest-message"
											>
												<span class="font-medium">{message.is_self ? 'You: ' : ''}</span
												>{message.message}
											</div>
										{:else if identity['last_message']}
											<div
												id="platform-list-chat-item-fallback-message-{identity.id}"
												class="mt-1 truncate text-sm {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-600'}"
												data-testid="chat-item-fallback-message"
											>
												{identity['last_message']}
											</div>
										{/if}
									</div>

									<!-- Right Side Info -->
									<div
										id="platform-list-chat-item-meta-{identity.id}"
										class="ml-2 flex flex-col items-end"
										data-testid="chat-item-meta"
									>
										<!-- Time -->
										{#if latestMessages.has(identity.id)}
											{@const message = latestMessages.get(identity.id)}
											<span
												id="platform-list-chat-item-time-{identity.id}"
												class="whitespace-nowrap text-xs {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-600'}"
												data-testid="chat-item-time"
											>
												{formatTime(message.created_on)}
											</span>
										{:else if identity['last_message_time']}
											<span
												id="platform-list-chat-item-fallback-time-{identity.id}"
												class="whitespace-nowrap text-xs {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-600'}"
												data-testid="chat-item-fallback-time"
											>
												{formatTime(identity['last_message_time'])}
											</span>
										{/if}

										<!-- Unread Count -->
										{#if unreadCounts.get(identity.id) > 0}
											<span
												id="platform-list-chat-item-unread-count-{identity.id}"
												class="mt-1 inline-flex h-5 min-w-[20px] items-center justify-center rounded-full
														 bg-red-500 px-1.5 text-xs font-bold text-white"
												data-testid="chat-item-unread-count"
											>
												{unreadCounts.get(identity.id)}
											</span>
										{:else if identity.unread_count > 0}
											<span
												id="platform-list-chat-item-fallback-unread-count-{identity.id}"
												class="mt-1 inline-flex h-5 min-w-[20px] items-center justify-center rounded-full
														 bg-red-500 px-1.5 text-xs font-bold text-white"
												data-testid="chat-item-fallback-unread-count"
											>
												{identity.unread_count}
											</span>
										{/if}
									</div>
								</div>
								<div
									id="platform-list-chat-item-badges-{identity.id}"
									class="mt-4 flex-row items-center gap-1 space-y-1 text-xs text-gray-700"
									data-testid="chat-item-badges"
								>
									<!-- Ticket Badge -->
									<Badge
										id="platform-list-chat-item-ticket-badge-{identity.id}"
										border
										color={selectedPlatformId === identity.id ? 'blue' : 'gray'}
										data-testid="chat-item-ticket-badge"
									>
										<TicketSolid
											class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
												? 'text-blue-600'
												: 'text-gray-600'}"
										/>
										{identity['latest_ticket_id']}
									</Badge>
									
									<!-- Priority Badge -->
									<Badge
										id="platform-list-chat-item-priority-badge-{identity.id}"
										border
										color={selectedPlatformId === identity.id ? 'blue' : 'gray'}
										data-testid="chat-item-priority-badge"
									>
										<ExclamationCircleOutline
											class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
												? 'text-blue-600'
												: 'text-gray-600'}"
										/>
										{identity['latest_ticket_priority']}
									</Badge>
									
									<!-- Owner Badge -->
									<Badge
										id="platform-list-chat-item-owner-badge-{identity.id}"
										border
										color={selectedPlatformId === identity.id ? 'blue' : 'gray'}
										data-testid="chat-item-owner-badge"
									>
										<UserHeadsetOutline
											class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
												? 'text-blue-600'
												: 'text-gray-600'}"
										/>
										{identity['latest_ticket_owner']?.split(/\s+/)[0] ?? ''}
									</Badge>

									<!-- Channel Badge -->
									<Badge
										id="platform-list-chat-item-channel-badge-{identity.id}"
										border
										color={selectedPlatformId === identity.id ? 'blue' : 'gray'}
										data-testid="chat-item-channel-badge"
										><BullhornOutline
											class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
												? 'text-blue-600'
												: 'text-gray-600'}"
										/>{identity.channel_name}</Badge
									>
									<!-- <Badge border color="gray">
										<TicketSolid class="mr-1 h-4 w-4 text-gray-600" />
										{identity['latest_ticket_priority']}
									</Badge> -->
									<!-- <Badge border color="gray">
										<TicketSolid class="mr-1 h-4 w-4 text-gray-600" />
										Status: {identity['latest_ticket_status']}
									</Badge> -->
								</div>
							</button>
						{/each}
					</div>

					<!-- Load More -->
					{#if hasMore}
						<div
							id="platform-list-infinite-scroll-container"
							data-testid="infinite-scroll-container"
						>
							<InfiniteScroll on:loadMore={handleLoadMore} loading={loadingMore} />
						</div>
					{/if}
				{/if}
			</div>
		</div>
	</div>
</div>

<style>
	.custom-scrollbar {
		scrollbar-width: thin;
		scrollbar-color: #e5e7eb #f9fafb;
	}

	.custom-scrollbar::-webkit-scrollbar {
		width: 6px;
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		background: #f9fafb;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		background-color: #e5e7eb;
		border-radius: 3px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb:hover {
		background-color: #d1d5db;
	}
</style>

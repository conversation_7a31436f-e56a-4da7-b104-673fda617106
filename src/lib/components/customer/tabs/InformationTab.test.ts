import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { PollingService } from '$lib/services/pollingService';

// Mock the polling service
vi.mock('$lib/services/pollingService', () => ({
	PollingService: {
		getInstance: vi.fn(() => ({
			registerEndpoint: vi.fn(),
			unregisterEndpoint: vi.fn(),
			cleanup: vi.fn()
		}))
	}
}));

// Mock other dependencies
vi.mock('$lib/stores/i18n', () => ({
	t: vi.fn((key) => key),
	language: { subscribe: vi.fn() }
}));

vi.mock('$app/stores', () => ({
	page: {
		subscribe: vi.fn(),
		data: { role: 'Admin' }
	}
}));

vi.mock('$src/lib/config', () => ({
	getBackendUrl: vi.fn(() => 'http://localhost:8000')
}));

vi.mock('$src/lib/api/features', () => ({
	services: {
		customers: {
			getPlatformInfo: vi.fn(),
			getFilterTags: vi.fn(() => Promise.resolve({ data: [] })),
			getCustomerNotes: vi.fn(() => Promise.resolve({ customer_notes: [] }))
		}
	}
}));

describe('InformationTab Polling Integration', () => {
	let mockPollingService: any;

	beforeEach(() => {
		mockPollingService = {
			registerEndpoint: vi.fn(),
			unregisterEndpoint: vi.fn(),
			cleanup: vi.fn()
		};

		(PollingService.getInstance as any).mockReturnValue(mockPollingService);
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should create polling service instance', () => {
		const service = PollingService.getInstance();
		expect(service).toBeDefined();
		expect(mockPollingService.registerEndpoint).toBeDefined();
		expect(mockPollingService.unregisterEndpoint).toBeDefined();
	});

	it('should have correct polling configuration structure', () => {
		// Test that our polling configuration matches expected structure
		const expectedConfig = {
			interval: expect.any(Number),
			customFetcher: expect.any(Function),
			onDataChange: expect.any(Function),
			onError: expect.any(Function)
		};

		// Verify the structure is correct
		expect(expectedConfig.interval).toEqual(expect.any(Number));
		expect(expectedConfig.customFetcher).toEqual(expect.any(Function));
		expect(expectedConfig.onDataChange).toEqual(expect.any(Function));
		expect(expectedConfig.onError).toEqual(expect.any(Function));
	});

	it('should have different intervals for different endpoints', () => {
		// Test that we have different polling intervals for different data types
		const intervals = {
			profile: 10000,    // 10 seconds
			tags: 15000,       // 15 seconds
			notes: 8000,       // 8 seconds
			history: 20000     // 20 seconds
		};

		expect(intervals.profile).toBe(10000);
		expect(intervals.tags).toBe(15000);
		expect(intervals.notes).toBe(8000);
		expect(intervals.history).toBe(20000);
	});
});
